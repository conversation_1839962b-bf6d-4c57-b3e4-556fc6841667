<#
.SYNOPSIS
    AURACRON Configurable Build System
    Advanced PowerShell script that reads BuildConfig.json for flexible builds

.DESCRIPTION
    This script provides a comprehensive build system for AURACRON that:
    - Reads configuration from BuildConfig.json
    - Supports multiple build configurations
    - Handles all project modules and bridges
    - Provides detailed logging and error handling
    - Supports CI/CD integration

.PARAMETER ConfigName
    Name of the build configuration to use (Development, Shipping, DebugGame)

.PARAMETER Platform
    Target platform (Win64, Android, IOS)

.PARAMETER ConfigFile
    Path to the configuration JSON file

.PARAMETER LogLevel
    Logging level (Minimal, Normal, Verbose)

.PARAMETER DryRun
    Show what would be done without executing

.EXAMPLE
    .\ConfigurableBuild.ps1 -ConfigName "Development" -Platform "Win64"
    
.EXAMPLE
    .\ConfigurableBuild.ps1 -ConfigName "Shipping" -Platform "Win64" -LogLevel "Verbose"
#>

param(
    [Parameter(Mandatory=$false)]
    [ValidateSet("Development", "Shipping", "DebugGame")]
    [string]$ConfigName = "Development",
    
    [Parameter(Mandatory=$false)]
    [ValidateSet("Win64", "Android", "IOS")]
    [string]$Platform = "Win64",
    
    [Parameter(Mandatory=$false)]
    [string]$ConfigFile = "BuildConfig.json",
    
    [Parameter(Mandatory=$false)]
    [ValidateSet("Minimal", "Normal", "Verbose")]
    [string]$LogLevel = "Normal",
    
    [Parameter(Mandatory=$false)]
    [switch]$DryRun,
    
    [Parameter(Mandatory=$false)]
    [switch]$Force
)

# Script configuration
$ErrorActionPreference = "Stop"
$ProgressPreference = "SilentlyContinue"

# Global variables
$script:Config = $null
$script:ProjectRoot = ""
$script:LogFile = ""
$script:StartTime = Get-Date

# Color functions
function Write-ColorText {
    param(
        [string]$Text,
        [ConsoleColor]$Color = [ConsoleColor]::White
    )
    $originalColor = $Host.UI.RawUI.ForegroundColor
    $Host.UI.RawUI.ForegroundColor = $Color
    Write-Host $Text
    $Host.UI.RawUI.ForegroundColor = $originalColor
}

function Write-Success { param([string]$Text) Write-ColorText $Text -Color Green }
function Write-Warning { param([string]$Text) Write-ColorText $Text -Color Yellow }
function Write-Error { param([string]$Text) Write-ColorText $Text -Color Red }
function Write-Info { param([string]$Text) Write-ColorText $Text -Color Cyan }
function Write-Debug { param([string]$Text) if ($LogLevel -eq "Verbose") { Write-ColorText "[DEBUG] $Text" -Color Gray } }

# Logging function
function Write-Log {
    param(
        [string]$Message,
        [ValidateSet("INFO", "WARNING", "ERROR", "DEBUG")]
        [string]$Level = "INFO"
    )
    
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logEntry = "[$timestamp] [$Level] $Message"
    
    # Write to console based on log level
    switch ($Level) {
        "INFO" { if ($LogLevel -ne "Minimal") { Write-Info $Message } }
        "WARNING" { Write-Warning $Message }
        "ERROR" { Write-Error $Message }
        "DEBUG" { Write-Debug $Message }
    }
    
    # Write to log file
    if ($script:LogFile -and (Test-Path (Split-Path $script:LogFile -Parent))) {
        Add-Content -Path $script:LogFile -Value $logEntry -Encoding UTF8
    }
}

# Load configuration
function Load-Configuration {
    param([string]$ConfigPath)
    
    Write-Log "Loading configuration from: $ConfigPath" "INFO"
    
    if (-not (Test-Path $ConfigPath)) {
        throw "Configuration file not found: $ConfigPath"
    }
    
    try {
        $configContent = Get-Content $ConfigPath -Raw -Encoding UTF8
        $config = $configContent | ConvertFrom-Json
        
        Write-Log "Configuration loaded successfully" "INFO"
        Write-Debug "Project: $($config.project.name) v$($config.project.version)"
        Write-Debug "Engine Version: $($config.project.engineVersion)"
        
        return $config
    }
    catch {
        throw "Failed to parse configuration file: $($_.Exception.Message)"
    }
}

# Initialize build environment
function Initialize-BuildEnvironment {
    Write-Log "Initializing build environment..." "INFO"
    
    # Set project root
    $script:ProjectRoot = Split-Path $PSScriptRoot -Parent
    Write-Debug "Project root: $script:ProjectRoot"
    
    # Create log directory
    $logDir = Join-Path $script:ProjectRoot $script:Config.logging.logDirectory
    if (-not (Test-Path $logDir)) {
        New-Item -Path $logDir -ItemType Directory -Force | Out-Null
        Write-Debug "Created log directory: $logDir"
    }
    
    # Set log file
    $logFileName = "Build_$ConfigName_$Platform_$(Get-Date -Format 'yyyyMMdd_HHmmss').log"
    $script:LogFile = Join-Path $logDir $logFileName
    Write-Debug "Log file: $script:LogFile"
    
    # Validate project structure
    $projectFile = Join-Path $script:ProjectRoot "$($script:Config.project.name).uproject"
    if (-not (Test-Path $projectFile)) {
        throw "Project file not found: $projectFile"
    }
    
    Write-Log "Build environment initialized successfully" "INFO"
}

# Find Unreal Engine installation
function Find-UnrealEngine {
    Write-Log "Locating Unreal Engine $($script:Config.project.engineVersion)..." "INFO"
    
    $enginePaths = @(
        "C:\Program Files\Epic Games\UE_$($script:Config.project.engineVersion)",
        "C:\UnrealEngine\UE_$($script:Config.project.engineVersion)",
        "D:\UnrealEngine\UE_$($script:Config.project.engineVersion)"
    )
    
    foreach ($path in $enginePaths) {
        if (Test-Path $path) {
            $ubtPath = Join-Path $path "Engine\Binaries\DotNET\UnrealBuildTool\UnrealBuildTool.exe"
            $uatPath = Join-Path $path "Engine\Build\BatchFiles\RunUAT.bat"
            
            if ((Test-Path $ubtPath) -and (Test-Path $uatPath)) {
                Write-Log "Found Unreal Engine at: $path" "INFO"
                return @{
                    EnginePath = $path
                    UBTPath = $ubtPath
                    UATPath = $uatPath
                }
            }
        }
    }
    
    throw "Unreal Engine $($script:Config.project.engineVersion) not found in standard locations"
}

# Generate project files
function Invoke-GenerateProjectFiles {
    param($EngineInfo)
    
    if (-not $script:Config.automation.generateProjectFiles) {
        Write-Log "Skipping project file generation (disabled in config)" "INFO"
        return
    }
    
    Write-Log "Generating project files..." "INFO"
    
    $projectFile = Join-Path $script:ProjectRoot "$($script:Config.project.name).uproject"
    $generateCmd = "\"$($EngineInfo.UBTPath)\" -projectfiles -project=\"$projectFile\" -game -rocket -progress"
    
    if ($DryRun) {
        Write-Info "[DRY RUN] Would execute: $generateCmd"
        return
    }
    
    Write-Debug "Executing: $generateCmd"
    
    try {
        $result = Invoke-Expression $generateCmd
        if ($LASTEXITCODE -eq 0) {
            Write-Log "Project files generated successfully" "INFO"
        } else {
            throw "Project file generation failed with exit code: $LASTEXITCODE"
        }
    }
    catch {
        Write-Log "Project file generation failed: $($_.Exception.Message)" "ERROR"
        throw
    }
}

# Build specific module
function Invoke-BuildModule {
    param(
        [string]$ModuleName,
        [hashtable]$EngineInfo,
        [hashtable]$BuildConfig
    )
    
    Write-Log "Building module: $ModuleName" "INFO"
    
    $projectFile = Join-Path $script:ProjectRoot "$($script:Config.project.name).uproject"
    $buildCmd = "\"$($EngineInfo.UBTPath)\" $ModuleName $($BuildConfig.platform) $($BuildConfig.configuration) -project=\"$projectFile\" -progress"
    
    if ($DryRun) {
        Write-Info "[DRY RUN] Would execute: $buildCmd"
        return $true
    }
    
    Write-Debug "Executing: $buildCmd"
    
    try {
        $result = Invoke-Expression $buildCmd
        if ($LASTEXITCODE -eq 0) {
            Write-Log "Module $ModuleName built successfully" "INFO"
            return $true
        } else {
            Write-Log "Module $ModuleName build failed with exit code: $LASTEXITCODE" "ERROR"
            return $false
        }
    }
    catch {
        Write-Log "Module $ModuleName build failed: $($_.Exception.Message)" "ERROR"
        return $false
    }
}

# Main build function
function Invoke-Build {
    try {
        Write-Log "Starting AURACRON build process..." "INFO"
        Write-Log "Configuration: $ConfigName" "INFO"
        Write-Log "Platform: $Platform" "INFO"
        
        # Get build configuration
        $buildConfig = $script:Config.buildConfigurations.$ConfigName
        if (-not $buildConfig) {
            throw "Build configuration '$ConfigName' not found"
        }
        
        # Get platform configuration
        $platformConfig = $script:Config.platforms.$Platform
        if (-not $platformConfig -or -not $platformConfig.enabled) {
            throw "Platform '$Platform' not found or not enabled"
        }
        
        # Find Unreal Engine
        $engineInfo = Find-UnrealEngine
        
        # Generate project files
        Invoke-GenerateProjectFiles -EngineInfo $engineInfo
        
        # Build modules
        $allModules = @()
        $allModules += $script:Config.modules.core
        $allModules += $script:Config.modules.bridges
        $allModules += $script:Config.modules.systems
        
        $successCount = 0
        $failureCount = 0
        
        foreach ($module in $allModules) {
            $success = Invoke-BuildModule -ModuleName $module -EngineInfo $engineInfo -BuildConfig $buildConfig
            if ($success) {
                $successCount++
            } else {
                $failureCount++
                if (-not $Force) {
                    throw "Build failed for module: $module"
                }
            }
        }
        
        # Summary
        Write-Log "Build completed!" "INFO"
        Write-Log "Modules built successfully: $successCount" "INFO"
        if ($failureCount -gt 0) {
            Write-Log "Modules failed: $failureCount" "WARNING"
        }
        
        $duration = (Get-Date) - $script:StartTime
        Write-Log "Total build time: $($duration.ToString('hh\:mm\:ss'))" "INFO"
        
    }
    catch {
        Write-Log "Build failed: $($_.Exception.Message)" "ERROR"
        throw
    }
}

# Main execution
try {
    Write-Success "========================================"
    Write-Success "AURACRON Configurable Build System"
    Write-Success "========================================"
    
    # Load configuration
    $configPath = Join-Path $PSScriptRoot $ConfigFile
    $script:Config = Load-Configuration -ConfigPath $configPath
    
    # Initialize environment
    Initialize-BuildEnvironment
    
    # Start build
    Invoke-Build
    
    Write-Success "========================================"
    Write-Success "BUILD COMPLETED SUCCESSFULLY!"
    Write-Success "========================================"
    
    exit 0
}
catch {
    Write-Error "========================================"
    Write-Error "BUILD FAILED!"
    Write-Error "Error: $($_.Exception.Message)"
    Write-Error "========================================"
    
    exit 1
}