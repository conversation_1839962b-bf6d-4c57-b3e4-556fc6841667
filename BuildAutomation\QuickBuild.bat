@echo off
REM AURACRON Quick Build Script
REM Simple batch file for quick development builds

setlocal EnableDelayedExpansion

echo ========================================
echo AURACRON Quick Build System
echo ========================================
echo.

REM Get script directory
set "SCRIPT_DIR=%~dp0"
set "PROJECT_ROOT=%SCRIPT_DIR%.."
set "BUILD_SCRIPT=%SCRIPT_DIR%BuildAuracron.ps1"

REM Check if PowerShell script exists
if not exist "%BUILD_SCRIPT%" (
    echo ERROR: Build script not found at %BUILD_SCRIPT%
    pause
    exit /b 1
)

REM Menu system
:MENU
cls
echo ========================================
echo AURACRON Quick Build Menu
echo ========================================
echo.
echo 1. Development Build (Editor + Game)
echo 2. Shipping Build (Game Only)
echo 3. Clean Development Build
echo 4. Headless Build (CI/CD)
echo 5. Package Game
echo 6. Exit
echo.
set /p choice="Select an option (1-6): "

if "%choice%"=="1" goto DEV_BUILD
if "%choice%"=="2" goto SHIPPING_BUILD
if "%choice%"=="3" goto CLEAN_BUILD
if "%choice%"=="4" goto HEADLESS_BUILD
if "%choice%"=="5" goto PACKAGE_BUILD
if "%choice%"=="6" goto EXIT

echo Invalid choice. Please try again.
pause
goto MENU

:DEV_BUILD
echo.
echo Starting Development Build...
echo.
powershell.exe -ExecutionPolicy Bypass -File "%BUILD_SCRIPT%" -Configuration Development -Platform Win64 -BuildEditor -BuildGame
goto BUILD_COMPLETE

:SHIPPING_BUILD
echo.
echo Starting Shipping Build...
echo.
powershell.exe -ExecutionPolicy Bypass -File "%BUILD_SCRIPT%" -Configuration Shipping -Platform Win64 -BuildGame
goto BUILD_COMPLETE

:CLEAN_BUILD
echo.
echo Starting Clean Development Build...
echo.
powershell.exe -ExecutionPolicy Bypass -File "%BUILD_SCRIPT%" -Configuration Development -Platform Win64 -BuildEditor -BuildGame -CleanBuild
goto BUILD_COMPLETE

:HEADLESS_BUILD
echo.
echo Starting Headless Build...
echo.
set "HEADLESS_SCRIPT=%SCRIPT_DIR%BuildHeadless.ps1"
if exist "%HEADLESS_SCRIPT%" (
    powershell.exe -ExecutionPolicy Bypass -File "%HEADLESS_SCRIPT%" -Configuration Development -Platform Win64 -BuildAllModules
) else (
    echo ERROR: Headless build script not found!
    pause
    goto MENU
)
goto BUILD_COMPLETE

:PACKAGE_BUILD
echo.
echo Starting Package Build...
echo.
powershell.exe -ExecutionPolicy Bypass -File "%BUILD_SCRIPT%" -Configuration Shipping -Platform Win64 -BuildGame -PackageGame
goto BUILD_COMPLETE

:BUILD_COMPLETE
echo.
if %ERRORLEVEL% EQU 0 (
    echo ========================================
    echo BUILD COMPLETED SUCCESSFULLY!
    echo ========================================
) else (
    echo ========================================
    echo BUILD FAILED!
    echo Error Code: %ERRORLEVEL%
    echo ========================================
)
echo.
echo Press any key to return to menu...
pause >nul
goto MENU

:EXIT
echo.
echo Goodbye!
echo.
exit /b 0