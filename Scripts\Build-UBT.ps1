<#
.SYNOPSIS
    Script de automação para build do projeto Auracron via UBT (Unreal Build Tool)
    
.DESCRIPTION
    Este script automatiza o processo de build do projeto Auracron usando o Unreal Build Tool (UBT)
    para Unreal Engine 5.6. Suporta múltiplas configurações, plataformas e validações.
    
.PARAMETER Configuration
    Configuração de build (Development, Shipping, DebugGame, Test)
    
.PARAMETER Platform
    Plataforma alvo (Win64, Linux, Mac, etc.)
    
.PARAMETER Target
    Tipo de target (Game, Editor, Server)
    
.PARAMETER Clean
    Força limpeza antes do build
    
.PARAMETER Verbose
    Habilita saída verbosa
    
.EXAMPLE
    .\Build-UBT.ps1 -Configuration Development -Platform Win64 -Target Game
    
.EXAMPLE
    .\Build-UBT.ps1 -Configuration Shipping -Platform Win64 -Target Game -Clean
#>

param(
    [Parameter(Mandatory=$false)]
    [ValidateSet("Development", "Shipping", "DebugGame", "Test", "DebugGameEditor")]
    [string]$Configuration = "Development",
    
    [Parameter(Mandatory=$false)]
    [ValidateSet("Win64", "Linux", "Mac", "Android", "iOS")]
    [string]$Platform = "Win64",
    
    [Parameter(Mandatory=$false)]
    [ValidateSet("Game", "Editor", "Server")]
    [string]$Target = "Game",
    
    [Parameter(Mandatory=$false)]
    [switch]$Clean,
    
    [Parameter(Mandatory=$false)]
    [switch]$VerboseOutput,
    
    [Parameter(Mandatory=$false)]
    [switch]$SkipValidation
)

# Configurações globais
$ErrorActionPreference = "Stop"
$ProjectRoot = "C:\Aura\projeto\Auracron"
$ProjectFile = "$ProjectRoot\Auracron.uproject"
$LogFile = "$ProjectRoot\Logs\Build-UBT-$(Get-Date -Format 'yyyyMMdd-HHmmss').log"

# Função para logging
function Write-Log {
    param(
        [string]$Message,
        [string]$Level = "INFO"
    )
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logMessage = "[$timestamp] [$Level] $Message"
    
    # Criar diretório de logs se não existir
    $logDir = Split-Path $LogFile -Parent
    if (!(Test-Path $logDir)) {
        New-Item -ItemType Directory -Path $logDir -Force | Out-Null
    }
    
    # Escrever no arquivo de log
    Add-Content -Path $LogFile -Value $logMessage
    
    # Escrever no console com cores
    switch ($Level) {
        "ERROR" { Write-Host $logMessage -ForegroundColor Red }
        "WARN" { Write-Host $logMessage -ForegroundColor Yellow }
        "SUCCESS" { Write-Host $logMessage -ForegroundColor Green }
        default { Write-Host $logMessage -ForegroundColor White }
    }
}

# Função para validar ambiente
function Test-Environment {
    Write-Log "Validando ambiente de desenvolvimento..."
    
    # Verificar se o projeto existe
    if (!(Test-Path $ProjectFile)) {
        Write-Log "Arquivo de projeto não encontrado: $ProjectFile" "ERROR"
        throw "Projeto Auracron não encontrado"
    }
    
    # Verificar Unreal Engine 5.6
    $ueVersion = "5.6"
    $uePath = $null
    
    # Tentar encontrar UE 5.6 em locais comuns
    $commonPaths = @(
        "C:\Program Files\Epic Games\UE_$ueVersion",
        "C:\UnrealEngine\UE_$ueVersion",
        "D:\UnrealEngine\UE_$ueVersion"
    )
    
    foreach ($path in $commonPaths) {
        if (Test-Path "$path\Engine\Binaries\DotNET\UnrealBuildTool\UnrealBuildTool.exe") {
            $uePath = $path
            break
        }
    }
    
    if (!$uePath) {
        Write-Log "Unreal Engine 5.6 não encontrado nos caminhos padrão" "ERROR"
        throw "UE 5.6 não instalado ou não encontrado"
    }
    
    $script:UBTPath = "$uePath\Engine\Binaries\DotNET\UnrealBuildTool\UnrealBuildTool.exe"
    Write-Log "UBT encontrado em: $script:UBTPath" "SUCCESS"
    
    # Verificar módulos do projeto
    $projectContent = Get-Content $ProjectFile | ConvertFrom-Json
    $moduleCount = $projectContent.Modules.Count
    Write-Log "Projeto contém $moduleCount módulos" "INFO"
    
    # Verificar bridges críticos
    $criticalBridges = @(
        "AuracronMasterOrchestrator",
        "AuracronDynamicRealmBridge",
        "AuracronPCGBridge",
        "AuracronSigilosBridge"
    )
    
    foreach ($bridge in $criticalBridges) {
        $bridgePath = "$ProjectRoot\Source\$bridge"
        if (!(Test-Path $bridgePath)) {
            Write-Log "Bridge crítico não encontrado: $bridge" "ERROR"
            throw "Bridge $bridge não encontrado"
        }
        Write-Log "Bridge verificado: $bridge" "SUCCESS"
    }
    
    return $true
}

# Função para limpeza
function Invoke-CleanBuild {
    Write-Log "Iniciando limpeza de build..."
    
    $cleanPaths = @(
        "$ProjectRoot\Binaries",
        "$ProjectRoot\Intermediate",
        "$ProjectRoot\Build"
    )
    
    foreach ($path in $cleanPaths) {
        if (Test-Path $path) {
            Write-Log "Removendo: $path"
            Remove-Item -Path $path -Recurse -Force -ErrorAction SilentlyContinue
        }
    }
    
    # Limpar caches de módulos
    $sourcePath = "$ProjectRoot\Source"
    Get-ChildItem -Path $sourcePath -Directory | ForEach-Object {
        $intermediatePath = "$($_.FullName)\Intermediate"
        if (Test-Path $intermediatePath) {
            Write-Log "Limpando cache do módulo: $($_.Name)"
            Remove-Item -Path $intermediatePath -Recurse -Force -ErrorAction SilentlyContinue
        }
    }
    
    Write-Log "Limpeza concluída" "SUCCESS"
}

# Função principal de build
function Invoke-UBTBuild {
    Write-Log "Iniciando build via UBT..."
    Write-Log "Configuração: $Configuration, Plataforma: $Platform, Target: $Target"
    
    # Construir argumentos do UBT
    $targetName = "Auracron"
    if ($Target -eq "Editor") {
        $targetName = "AuracronEditor"
    } elseif ($Target -eq "Server") {
        $targetName = "AuracronServer"
    }
    
    $ubtArgs = @(
        $targetName,
        $Platform,
        $Configuration,
        "-Project=`"$ProjectFile`"",
        "-WaitMutex",
        "-FromMsBuild"
    )
    
    if ($VerboseOutput) {
        $ubtArgs += "-Verbose"
    }
    
    # Adicionar flags específicas para UE 5.6
    $ubtArgs += @(
        "-DisableUnity",
        "-ForceUnity",
        "-UsePrecompiled"
    )
    
    Write-Log "Executando: $script:UBTPath $($ubtArgs -join ' ')"
    
    try {
        $process = Start-Process -FilePath $script:UBTPath -ArgumentList $ubtArgs -Wait -PassThru -NoNewWindow -RedirectStandardOutput "$ProjectRoot\Logs\UBT-Output.log" -RedirectStandardError "$ProjectRoot\Logs\UBT-Error.log"
        
        if ($process.ExitCode -eq 0) {
            Write-Log "Build concluído com sucesso!" "SUCCESS"
            return $true
        } else {
            Write-Log "Build falhou com código de saída: $($process.ExitCode)" "ERROR"
            
            # Mostrar erros se existirem
            $errorLog = "$ProjectRoot\Logs\UBT-Error.log"
            if (Test-Path $errorLog) {
                $errors = Get-Content $errorLog
                if ($errors) {
                    Write-Log "Erros encontrados:" "ERROR"
                    $errors | ForEach-Object { Write-Log $_ "ERROR" }
                }
            }
            return $false
        }
    } catch {
        Write-Log "Erro ao executar UBT: $($_.Exception.Message)" "ERROR"
        return $false
    }
}

# Função para validar resultado do build
function Test-BuildResult {
    Write-Log "Validando resultado do build..."
    
    $expectedBinaries = @()
    
    switch ($Target) {
        "Game" {
            $expectedBinaries += "$ProjectRoot\Binaries\$Platform\Auracron.exe"
        }
        "Editor" {
            $expectedBinaries += "$ProjectRoot\Binaries\$Platform\UnrealEditor-Auracron.dll"
        }
        "Server" {
            $expectedBinaries += "$ProjectRoot\Binaries\$Platform\AuracronServer.exe"
        }
    }
    
    $allFound = $true
    foreach ($binary in $expectedBinaries) {
        if (Test-Path $binary) {
            $fileInfo = Get-Item $binary
            Write-Log "Binário encontrado: $($fileInfo.Name) ($(($fileInfo.Length / 1MB).ToString('F2')) MB)" "SUCCESS"
        } else {
            Write-Log "Binário esperado não encontrado: $binary" "ERROR"
            $allFound = $false
        }
    }
    
    return $allFound
}

# Função principal
function Main {
    try {
        Write-Log "=== INICIANDO BUILD AURACRON VIA UBT ===" "INFO"
        Write-Log "Timestamp: $(Get-Date)" "INFO"
        
        # Validar ambiente se não foi pulado
        if (!$SkipValidation) {
            Test-Environment
        }
        
        # Limpeza se solicitada
        if ($Clean) {
            Invoke-CleanBuild
        }
        
        # Executar build
        $buildSuccess = Invoke-UBTBuild
        
        if ($buildSuccess) {
            # Validar resultado
            $validationSuccess = Test-BuildResult
            
            if ($validationSuccess) {
                Write-Log "=== BUILD CONCLUÍDO COM SUCESSO ===" "SUCCESS"
                Write-Log "Log completo salvo em: $LogFile" "INFO"
                exit 0
            } else {
                Write-Log "=== BUILD FALHOU NA VALIDAÇÃO ===" "ERROR"
                exit 1
            }
        } else {
            Write-Log "=== BUILD FALHOU ===" "ERROR"
            exit 1
        }
        
    } catch {
        Write-Log "Erro fatal: $($_.Exception.Message)" "ERROR"
        Write-Log "Stack trace: $($_.ScriptStackTrace)" "ERROR"
        exit 1
    }
}

# Executar script principal
Main