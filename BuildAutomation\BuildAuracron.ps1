# AURACRON Automated Build Script
# Builds the AURACRON project using UBT and RunUAT for Unreal Engine 5.6
# Author: AI Assistant
# Date: $(Get-Date)

param(
    [string]$Configuration = "Development",
    [string]$Platform = "Win64",
    [string]$EngineVersion = "5.6",
    [switch]$CleanBuild = $false,
    [switch]$BuildEditor = $true,
    [switch]$BuildGame = $true,
    [switch]$PackageGame = $false,
    [string]$OutputDirectory = "Builds"
)

# Script configuration
$ErrorActionPreference = "Stop"
$ProjectName = "Auracron"
$ProjectRoot = Split-Path -Parent $PSScriptRoot
$ProjectFile = Join-Path $ProjectRoot "$ProjectName.uproject"

# Colors for output
function Write-ColorOutput {
    param(
        [string]$Message,
        [string]$Color = "White"
    )
    Write-Host $Message -ForegroundColor $Color
}

# Function to find Unreal Engine installation
function Get-UnrealEngineInstallation {
    param([string]$Version)
    
    Write-ColorOutput "Locating Unreal Engine $Version installation..." "Yellow"
    
    # Try to find UE installation via registry
    $RegistryPath = "HKLM:\SOFTWARE\EpicGames\Unreal Engine\$Version"
    
    if (Test-Path $RegistryPath) {
        $InstallDir = (Get-ItemProperty -Path $RegistryPath -Name "InstalledDirectory" -ErrorAction SilentlyContinue).InstalledDirectory
        if ($InstallDir -and (Test-Path $InstallDir)) {
            Write-ColorOutput "Found Unreal Engine at: $InstallDir" "Green"
            return $InstallDir
        }
    }
    
    # Fallback: Check common installation paths
    $CommonPaths = @(
        "C:\Program Files\Epic Games\UE_$Version",
        "C:\Program Files (x86)\Epic Games\UE_$Version",
        "D:\Epic Games\UE_$Version",
        "C:\UnrealEngine\$Version"
    )
    
    foreach ($Path in $CommonPaths) {
        if (Test-Path $Path) {
            Write-ColorOutput "Found Unreal Engine at: $Path" "Green"
            return $Path
        }
    }
    
    throw "Unreal Engine $Version installation not found!"
}

# Function to validate project structure
function Test-ProjectStructure {
    Write-ColorOutput "Validating project structure..." "Yellow"
    
    if (-not (Test-Path $ProjectFile)) {
        throw "Project file not found: $ProjectFile"
    }
    
    $SourceDir = Join-Path $ProjectRoot "Source"
    if (-not (Test-Path $SourceDir)) {
        throw "Source directory not found: $SourceDir"
    }
    
    Write-ColorOutput "Project structure validated successfully" "Green"
}

# Function to clean build artifacts
function Invoke-CleanBuild {
    Write-ColorOutput "Cleaning build artifacts..." "Yellow"
    
    $DirsToClean = @(
        "Binaries",
        "Intermediate",
        "Build",
        ".vs"
    )
    
    foreach ($Dir in $DirsToClean) {
        $FullPath = Join-Path $ProjectRoot $Dir
        if (Test-Path $FullPath) {
            Write-ColorOutput "Removing: $FullPath" "Cyan"
            Remove-Item -Path $FullPath -Recurse -Force
        }
    }
    
    Write-ColorOutput "Clean completed" "Green"
}

# Main build function
function Invoke-Build {
    try {
        Write-ColorOutput "=== AURACRON BUILD AUTOMATION STARTED ===" "Magenta"
        Write-ColorOutput "Configuration: $Configuration" "White"
        Write-ColorOutput "Platform: $Platform" "White"
        Write-ColorOutput "Engine Version: $EngineVersion" "White"
        
        # Validate project
        Test-ProjectStructure
        
        # Get Unreal Engine installation
        $EngineDir = Get-UnrealEngineInstallation -Version $EngineVersion
        $UBTPath = Join-Path $EngineDir "Engine\Binaries\DotNET\UnrealBuildTool\UnrealBuildTool.exe"
        $RunUATPath = Join-Path $EngineDir "Engine\Build\BatchFiles\RunUAT.bat"
        
        # Validate tools
        if (-not (Test-Path $UBTPath)) {
            throw "UnrealBuildTool not found at: $UBTPath"
        }
        
        if (-not (Test-Path $RunUATPath)) {
            throw "RunUAT not found at: $RunUATPath"
        }
        
        # Clean build if requested
        if ($CleanBuild) {
            Invoke-CleanBuild
        }
        
        # Build Editor if requested
        if ($BuildEditor) {
            Write-ColorOutput "Building Editor..." "Yellow"
            $EditorArgs = @(
                "$ProjectName`Editor",
                $Platform,
                $Configuration,
                "-project=`"$ProjectFile`"",
                "-progress",
                "-NoHotReloadFromIDE"
            )
            
            Write-ColorOutput "Executing: $UBTPath $($EditorArgs -join ' ')" "Cyan"
            & $UBTPath @EditorArgs
            
            if ($LASTEXITCODE -ne 0) {
                throw "Editor build failed with exit code: $LASTEXITCODE"
            }
            
            Write-ColorOutput "Editor build completed successfully" "Green"
        }
        
        # Build Game if requested
        if ($BuildGame) {
            Write-ColorOutput "Building Game..." "Yellow"
            $GameArgs = @(
                $ProjectName,
                $Platform,
                $Configuration,
                "-project=`"$ProjectFile`"",
                "-progress"
            )
            
            Write-ColorOutput "Executing: $UBTPath $($GameArgs -join ' ')" "Cyan"
            & $UBTPath @GameArgs
            
            if ($LASTEXITCODE -ne 0) {
                throw "Game build failed with exit code: $LASTEXITCODE"
            }
            
            Write-ColorOutput "Game build completed successfully" "Green"
        }
        
        # Package Game if requested
        if ($PackageGame) {
            Write-ColorOutput "Packaging Game..." "Yellow"
            
            $OutputPath = Join-Path $ProjectRoot $OutputDirectory
            if (-not (Test-Path $OutputPath)) {
                New-Item -Path $OutputPath -ItemType Directory -Force | Out-Null
            }
            
            $PackageArgs = @(
                "BuildCookRun",
                "-project=`"$ProjectFile`"",
                "-platform=$Platform",
                "-configuration=$Configuration",
                "-cook",
                "-build",
                "-stage",
                "-package",
                "-archive",
                "-archivedirectory=`"$OutputPath`"",
                "-rocket"
            )
            
            Write-ColorOutput "Executing: $RunUATPath $($PackageArgs -join ' ')" "Cyan"
            & $RunUATPath @PackageArgs
            
            if ($LASTEXITCODE -ne 0) {
                throw "Packaging failed with exit code: $LASTEXITCODE"
            }
            
            Write-ColorOutput "Packaging completed successfully" "Green"
        }
        
        Write-ColorOutput "=== BUILD COMPLETED SUCCESSFULLY ===" "Green"
        
    } catch {
        Write-ColorOutput "=== BUILD FAILED ===" "Red"
        Write-ColorOutput "Error: $($_.Exception.Message)" "Red"
        exit 1
    }
}

# Execute build
Invoke-Build