{"project": {"name": "<PERSON><PERSON><PERSON>", "version": "1.0.0", "description": "AURACRON - Revolutionary 5v5 MOBA with Dynamic Realms", "engineVersion": "5.6"}, "buildConfigurations": {"Development": {"platform": "Win64", "configuration": "Development", "buildEditor": true, "buildGame": true, "packageGame": false, "runTests": false, "cleanBuild": false}, "Shipping": {"platform": "Win64", "configuration": "Shipping", "buildEditor": false, "buildGame": true, "packageGame": true, "runTests": true, "cleanBuild": true}, "DebugGame": {"platform": "Win64", "configuration": "DebugGame", "buildEditor": true, "buildGame": true, "packageGame": false, "runTests": false, "cleanBuild": false}}, "modules": {"core": ["<PERSON><PERSON><PERSON>", "AuracronEditor"], "bridges": ["AuracronDynamicRealmBridge", "AuracronHarmonyEngineBridge", "Auracron<PERSON><PERSON>los<PERSON>ridge", "AuracronVerticalTransitionsBridge", "AuracronAdaptiveCreaturesBridge", "AuracronAbismoUmbrioBridge", "AuracronFirmamentoZephyrBridge", "AuracronPlanicieRadianteBridge"], "systems": ["AuracronMasterOrchestrator", "AuracronNetworkingBridge", "AuracronPerformanceBridge", "AuracronSecurityBridge"]}, "platforms": {"Win64": {"enabled": true, "targetPlatform": "Win64", "architectures": ["x64"], "optimizations": {"useUnityBuild": true, "enablePCH": true, "enableLTO": false}}, "Android": {"enabled": true, "targetPlatform": "Android", "architectures": ["arm64", "armv7"], "optimizations": {"useUnityBuild": true, "enablePCH": false, "enableLTO": true}}, "IOS": {"enabled": false, "targetPlatform": "IOS", "architectures": ["arm64"], "optimizations": {"useUnityBuild": true, "enablePCH": false, "enableLTO": true}}}, "automation": {"generateProjectFiles": true, "buildAllModules": true, "runAutomatedTests": false, "createPackage": false, "uploadToSteam": false, "uploadToEpicStore": false}, "testing": {"unitTests": {"enabled": true, "timeout": 300, "parallel": true}, "functionalTests": {"enabled": true, "timeout": 600, "parallel": false}, "performanceTests": {"enabled": false, "timeout": 1200, "parallel": false}}, "packaging": {"outputDirectory": "Builds", "compressionLevel": "Normal", "includeDebugFiles": false, "includeCrashReporter": true, "includePrerequisites": true, "createInstaller": false}, "logging": {"level": "Normal", "outputToFile": true, "outputToConsole": true, "logDirectory": "BuildAutomation/Logs", "retainLogDays": 30}, "notifications": {"email": {"enabled": false, "recipients": [], "onSuccess": false, "onFailure": true}, "slack": {"enabled": false, "webhook": "", "channel": "#builds", "onSuccess": true, "onFailure": true}}, "deployment": {"steam": {"enabled": false, "appId": "", "depot": "", "branch": "main"}, "epicStore": {"enabled": false, "productId": "", "sandboxId": ""}, "customServers": {"enabled": false, "servers": []}}}