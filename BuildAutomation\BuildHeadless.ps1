# AURACRON Headless Build Script
# Automated headless building for CI/CD pipelines
# Supports Unreal Engine 5.6 with advanced automation features

param(
    [string]$Configuration = "Development",
    [string]$Platform = "Win64",
    [string]$EngineVersion = "5.6",
    [switch]$GenerateProjectFiles = $false,
    [switch]$BuildAllModules = $true,
    [switch]$RunTests = $false,
    [switch]$CreatePackage = $false,
    [string]$LogLevel = "Normal",
    [string]$OutputDir = "HeadlessBuild"
)

# Script configuration
$ErrorActionPreference = "Stop"
$ProjectName = "Auracron"
$ProjectRoot = Split-Path -Parent $PSScriptRoot
$ProjectFile = Join-Path $ProjectRoot "$ProjectName.uproject"
$LogFile = Join-Path $ProjectRoot "BuildAutomation\Logs\HeadlessBuild_$(Get-Date -Format 'yyyyMMdd_HHmmss').log"

# Ensure log directory exists
$LogDir = Split-Path -Parent $LogFile
if (-not (Test-Path $LogDir)) {
    New-Item -Path $LogDir -ItemType Directory -Force | Out-Null
}

# Logging function
function Write-BuildLog {
    param(
        [string]$Message,
        [string]$Level = "INFO",
        [string]$Color = "White"
    )
    
    $Timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $LogEntry = "[$Timestamp] [$Level] $Message"
    
    # Write to console with color
    Write-Host $LogEntry -ForegroundColor $Color
    
    # Write to log file
    Add-Content -Path $LogFile -Value $LogEntry
}

# Function to get Unreal Engine paths
function Get-UnrealEnginePaths {
    param([string]$Version)
    
    Write-BuildLog "Locating Unreal Engine $Version..." "INFO" "Yellow"
    
    # Registry lookup
    $RegistryPath = "HKLM:\SOFTWARE\EpicGames\Unreal Engine\$Version"
    
    if (Test-Path $RegistryPath) {
        $InstallDir = (Get-ItemProperty -Path $RegistryPath -Name "InstalledDirectory" -ErrorAction SilentlyContinue).InstalledDirectory
        if ($InstallDir -and (Test-Path $InstallDir)) {
            $Paths = @{
                EngineDir = $InstallDir
                UBT = Join-Path $InstallDir "Engine\Binaries\DotNET\UnrealBuildTool\UnrealBuildTool.exe"
                RunUAT = Join-Path $InstallDir "Engine\Build\BatchFiles\RunUAT.bat"
                UHT = Join-Path $InstallDir "Engine\Binaries\Win64\UnrealHeaderTool.exe"
                Editor = Join-Path $InstallDir "Engine\Binaries\Win64\UnrealEditor.exe"
                GenerateProjectFiles = Join-Path $InstallDir "Engine\Binaries\DotNET\UnrealBuildTool\UnrealBuildTool.exe"
            }
            
            Write-BuildLog "Found Unreal Engine at: $InstallDir" "INFO" "Green"
            return $Paths
        }
    }
    
    throw "Unreal Engine $Version not found in registry or installation directory!"
}

# Function to validate all required tools
function Test-BuildEnvironment {
    param($EnginePaths)
    
    Write-BuildLog "Validating build environment..." "INFO" "Yellow"
    
    $RequiredTools = @(
        @{ Name = "UnrealBuildTool"; Path = $EnginePaths.UBT },
        @{ Name = "RunUAT"; Path = $EnginePaths.RunUAT },
        @{ Name = "UnrealEditor"; Path = $EnginePaths.Editor }
    )
    
    foreach ($Tool in $RequiredTools) {
        if (-not (Test-Path $Tool.Path)) {
            throw "$($Tool.Name) not found at: $($Tool.Path)"
        }
        Write-BuildLog "✓ $($Tool.Name) found" "INFO" "Green"
    }
    
    # Validate project file
    if (-not (Test-Path $ProjectFile)) {
        throw "Project file not found: $ProjectFile"
    }
    Write-BuildLog "✓ Project file validated" "INFO" "Green"
    
    Write-BuildLog "Build environment validation completed" "INFO" "Green"
}

# Function to generate project files
function Invoke-GenerateProjectFiles {
    param($EnginePaths)
    
    Write-BuildLog "Generating project files..." "INFO" "Yellow"
    
    $Args = @(
        "-projectfiles",
        "-project=`"$ProjectFile`"",
        "-game",
        "-rocket",
        "-progress"
    )
    
    Write-BuildLog "Executing: $($EnginePaths.GenerateProjectFiles) $($Args -join ' ')" "INFO" "Cyan"
    
    $Process = Start-Process -FilePath $EnginePaths.GenerateProjectFiles -ArgumentList $Args -Wait -PassThru -NoNewWindow
    
    if ($Process.ExitCode -ne 0) {
        throw "Project file generation failed with exit code: $($Process.ExitCode)"
    }
    
    Write-BuildLog "Project files generated successfully" "INFO" "Green"
}

# Function to build specific modules
function Invoke-ModuleBuild {
    param(
        $EnginePaths,
        [string]$ModuleName,
        [string]$Config,
        [string]$Plat
    )
    
    Write-BuildLog "Building module: $ModuleName ($Config|$Plat)" "INFO" "Yellow"
    
    $Args = @(
        $ModuleName,
        $Plat,
        $Config,
        "-project=`"$ProjectFile`"",
        "-progress",
        "-NoHotReloadFromIDE",
        "-WaitMutex"
    )
    
    if ($LogLevel -eq "Verbose") {
        $Args += "-Verbose"
    }
    
    Write-BuildLog "Executing: $($EnginePaths.UBT) $($Args -join ' ')" "INFO" "Cyan"
    
    $Process = Start-Process -FilePath $EnginePaths.UBT -ArgumentList $Args -Wait -PassThru -NoNewWindow
    
    if ($Process.ExitCode -ne 0) {
        throw "Module build failed: $ModuleName (Exit Code: $($Process.ExitCode))"
    }
    
    Write-BuildLog "Module built successfully: $ModuleName" "INFO" "Green"
}

# Function to run automated tests
function Invoke-AutomatedTests {
    param($EnginePaths)
    
    Write-BuildLog "Running automated tests..." "INFO" "Yellow"
    
    $TestArgs = @(
        "BuildCookRun",
        "-project=`"$ProjectFile`"",
        "-platform=$Platform",
        "-configuration=$Configuration",
        "-test",
        "-automated",
        "-nullrhi",
        "-nosound",
        "-noP4",
        "-rocket"
    )
    
    Write-BuildLog "Executing tests: $($EnginePaths.RunUAT) $($TestArgs -join ' ')" "INFO" "Cyan"
    
    $Process = Start-Process -FilePath $EnginePaths.RunUAT -ArgumentList $TestArgs -Wait -PassThru -NoNewWindow
    
    if ($Process.ExitCode -ne 0) {
        Write-BuildLog "Some tests failed (Exit Code: $($Process.ExitCode))" "WARN" "Yellow"
    } else {
        Write-BuildLog "All tests passed successfully" "INFO" "Green"
    }
}

# Function to create deployment package
function Invoke-CreatePackage {
    param($EnginePaths)
    
    Write-BuildLog "Creating deployment package..." "INFO" "Yellow"
    
    $OutputPath = Join-Path $ProjectRoot $OutputDir
    if (-not (Test-Path $OutputPath)) {
        New-Item -Path $OutputPath -ItemType Directory -Force | Out-Null
    }
    
    $PackageArgs = @(
        "BuildCookRun",
        "-project=`"$ProjectFile`"",
        "-platform=$Platform",
        "-configuration=$Configuration",
        "-cook",
        "-build",
        "-stage",
        "-package",
        "-archive",
        "-archivedirectory=`"$OutputPath`"",
        "-rocket",
        "-compressed"
    )
    
    Write-BuildLog "Executing packaging: $($EnginePaths.RunUAT) $($PackageArgs -join ' ')" "INFO" "Cyan"
    
    $Process = Start-Process -FilePath $EnginePaths.RunUAT -ArgumentList $PackageArgs -Wait -PassThru -NoNewWindow
    
    if ($Process.ExitCode -ne 0) {
        throw "Packaging failed with exit code: $($Process.ExitCode)"
    }
    
    Write-BuildLog "Package created successfully at: $OutputPath" "INFO" "Green"
}

# Main headless build function
function Invoke-HeadlessBuild {
    try {
        Write-BuildLog "=== AURACRON HEADLESS BUILD STARTED ===" "INFO" "Magenta"
        Write-BuildLog "Configuration: $Configuration" "INFO" "White"
        Write-BuildLog "Platform: $Platform" "INFO" "White"
        Write-BuildLog "Engine Version: $EngineVersion" "INFO" "White"
        Write-BuildLog "Log File: $LogFile" "INFO" "White"
        
        # Get Unreal Engine paths
        $EnginePaths = Get-UnrealEnginePaths -Version $EngineVersion
        
        # Validate environment
        Test-BuildEnvironment -EnginePaths $EnginePaths
        
        # Generate project files if requested
        if ($GenerateProjectFiles) {
            Invoke-GenerateProjectFiles -EnginePaths $EnginePaths
        }
        
        # Build all modules if requested
        if ($BuildAllModules) {
            # Core modules to build
            $ModulesToBuild = @(
                "$($ProjectName)Editor",
                $ProjectName,
                "AuracronDynamicRealmBridge",
                "AuracronHarmonyEngineBridge",
                "AuracronSigilosBridge",
                "AuracronVerticalTransitionsBridge"
            )
            
            foreach ($Module in $ModulesToBuild) {
                try {
                    Invoke-ModuleBuild -EnginePaths $EnginePaths -ModuleName $Module -Config $Configuration -Plat $Platform
                } catch {
                    Write-BuildLog "Failed to build module $Module`: $($_.Exception.Message)" "WARN" "Yellow"
                    # Continue with other modules
                }
            }
        }
        
        # Run tests if requested
        if ($RunTests) {
            Invoke-AutomatedTests -EnginePaths $EnginePaths
        }
        
        # Create package if requested
        if ($CreatePackage) {
            Invoke-CreatePackage -EnginePaths $EnginePaths
        }
        
        Write-BuildLog "=== HEADLESS BUILD COMPLETED SUCCESSFULLY ===" "INFO" "Green"
        
    } catch {
        Write-BuildLog "=== HEADLESS BUILD FAILED ===" "ERROR" "Red"
        Write-BuildLog "Error: $($_.Exception.Message)" "ERROR" "Red"
        Write-BuildLog "Stack Trace: $($_.ScriptStackTrace)" "ERROR" "Red"
        exit 1
    }
}

# Execute headless build
Invoke-HeadlessBuild